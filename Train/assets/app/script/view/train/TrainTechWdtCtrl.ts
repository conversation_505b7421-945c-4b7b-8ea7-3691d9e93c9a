import { TrainTechCfg } from "../../common/constant/DataType";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class TrainTechWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected lineNode_: cc.Node = null // path://root/line_n
    protected treeSv_: cc.ScrollView = null // path://root/tree_sv
    //@end

    _reversed: boolean = false
    _selectedBtn: cc.Node = null

    public listenEventMaps() {
        return [
            { [EventType.TRAIN_TECH_UPGRADE]: this.onTechUpgrade }
        ]
    }

    public async onCreate() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    onTechUpgrade(id: string) {
        this.testLoadJson()
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    public init() {
        this.testLoadJson()
    }

    /**
     * 基于依赖关系构建id到行列的映射
     */
    private buildRowColMapping(ary: TrainTechCfg[]): { [id: number]: { row: number, col: number } } {
        const idToRowCol: { [id: number]: { row: number, col: number } } = {}
        const idToTech: { [id: number]: TrainTechCfg } = {}

        // 建立id到tech的映射
        for (const tech of ary) {
            idToTech[tech.id] = tech
        }

        // 计算每个节点的行数
        const calculateRow = (id: number): number => {
            if (idToRowCol[id]) {
                return idToRowCol[id].row
            }

            const tech = idToTech[id]
            if (!tech.pre || tech.pre.length === 0) {
                // 没有依赖的节点在第1行
                return 1
            }

            // 有依赖的节点，找到依赖节点的最大行数+1
            let maxPreRow = 0
            for (const preId of tech.pre) {
                const preRow = calculateRow(preId)
                maxPreRow = Math.max(maxPreRow, preRow)
            }
            return maxPreRow + 1
        }

        // 计算所有节点的行数
        for (const tech of ary) {
            const row = calculateRow(tech.id)
            idToRowCol[tech.id] = { row, col: 0 }
        }

        // 按行分组，计算列数
        const rowGroups: { [row: number]: number[] } = {}
        for (const tech of ary) {
            const row = idToRowCol[tech.id].row
            if (!rowGroups[row]) {
                rowGroups[row] = []
            }
            rowGroups[row].push(tech.id)
        }

        // 为每行的节点分配列号（按在配置数组中的出现顺序）
        for (const row in rowGroups) {
            const ids = rowGroups[row]
            // 按在原数组中的顺序排序
            ids.sort((a, b) => {
                const indexA = ary.findIndex(tech => tech.id === a)
                const indexB = ary.findIndex(tech => tech.id === b)
                return indexA - indexB
            })

            // 分配列号
            ids.forEach((id, index) => {
                idToRowCol[id].col = index + 1
            })
        }

        return idToRowCol
    }

    async testLoadJson() {
        if (this._reversed) {
            this.treeSv_.content.children.reverse()
        }
        const ary = assetsMgr.getJson<TrainTechCfg>("TrainTech").datas

        // 构建id到"行-列"的映射
        const idToRowCol = this.buildRowColMapping(ary)

        const dataMap = {}
        const preAry = []
        // 建立行列到id的反向映射
        const rowColToId: { [key: string]: number } = {}

        for (const tech of ary) {
            const id = tech.id
            const pre = tech.pre || []
            const rowCol = idToRowCol[id]
            const [row, ceil] = [rowCol.row.toString(), rowCol.col.toString()]
            dataMap[row] = dataMap[row] || { ceil: [] }
            dataMap[row].ceil.push({ ceil, id })  // 保存列号和对应的id
            rowColToId[`${row}-${ceil}`] = id  // 建立"行-列"到id的映射
            preAry.push(...pre.map(s => { return { cur: id, pre: s } }))
        }
        const tmp = [5, 3, 1, 2, 4]
        this.treeSv_.Items(Object.keys(dataMap), (lineNode: cc.Node, row: string) => {
            lineNode.name = row

            lineNode.Items(tmp, (it: cc.Node, arg: number) => {
                it.name = arg + ""
                it.active = false
            })
            const ceils = dataMap[row].ceil
            const refAry = []
            for (const ceilInfo of ceils) {
                const { ceil, id } = ceilInfo
                const tech = ary.find(t => t.id == id)
                if (tech.pre?.length) {
                    // 检查是否有同行的依赖关系
                    const ref = tech.pre.find(preId => {
                        const preRowCol = idToRowCol[preId]
                        return preRowCol && preRowCol.row.toString() == row
                    })
                    if (ref) {
                        refAry.push(ceilInfo)
                        continue
                    }
                }
                const it = lineNode.Child(ceil)
                it.active = true
                it.Data = id
            }
            const lyt = lineNode.getComponent(cc.Layout)
            lyt.updateLayout()
            lyt.enabled = false
            for (const ceilInfo of refAry) {
                const { ceil, id } = ceilInfo
                const it = lineNode.Child(ceil)
                const tech = ary.find(t => t.id == id)
                // 找到同行的依赖节点
                const preId = tech.pre.find(preId => {
                    const preRowCol = idToRowCol[preId]
                    return preRowCol && preRowCol.row.toString() == row
                })
                const preRowCol = idToRowCol[preId]
                const preCeil = preRowCol.col.toString()
                const preNode = lineNode.Child(preCeil)
                const isLeft = tmp.indexOf(Number(preCeil)) > tmp.indexOf(Number(ceil))
                const space = lyt.spacingX + lyt.paddingLeft + preNode.width
                it.x = preNode.x + (isLeft ? -space : space)
                it.setSiblingIndex(preNode.getSiblingIndex() - 1)
                it.active = true
                it.Data = id
            }
            lineNode.children.forEach(it => {
                const id = it.Data as number
                if (!id || !it.active) return
                const lv = gameHelper.trainTech.getTechLevel(id.toString())
                if (lv > 0) {
                    it.Child("lv").active = true
                    it.Child("lv").setLocaleKey("common_guiText_11", lv)
                    it.Child("icon").setGray(false)
                    it.Child("icon").setDark(0)
                    it.Child("lock").active = false
                }
                else if (gameHelper.trainTech.checkPre(id.toString())) {
                    it.Child("lv").active = false
                    it.Child("lock").active = false
                    it.Child("icon").setGray(false)
                    it.Child("icon").setDark(0.4)
                    it.Child("icon").opacity = 204
                }
                else {
                    it.Child("lv").active = false
                    it.Child("icon").setGray(true)
                    it.Child("icon").setDark(0.4)
                    it.Child("lock").active = true
                }
                it.Child("selected").active = false
                const cfg = assetsMgr.getJsonData<TrainTechCfg>("TrainTech", id)
                resHelper.loadTmpIcon(`tech/${cfg.icon}`, it.Child("icon", cc.Sprite), this.getTag())
                it.off("click")
                it.on("click", this.onClickHandler)
            })

        })
        const lyt = this.treeSv_.content.getComponent(cc.Layout)
        lyt.updateLayout()
        lyt.enabled = false

        this.treeSv_.content.children.reverse()
        this._reversed = true

        await ut.waitNextFrame(1, this)
        this.testRenderLine(preAry)
    }

    testRenderLine(preAry: { cur: string, pre: string }[]) {
        for (const { cur, pre } of preAry) {
            let [curRow, curCeil] = cur.split("-").map(s => Number(s))
            let [preRow, preCeil] = pre.split("-").map(s => Number(s))
            const curNode = this.treeSv_.content.Child(curRow).Child(curCeil)
            const preNode = this.treeSv_.content.Child(preRow).Child(preCeil)
            const name = `${cur}&${pre}`
            let lineNode = curNode.Child(name)
            if (!lineNode) {
                lineNode = cc.instantiate(this.lineNode_)
            }
            lineNode.active = true
            lineNode.parent = curNode
            lineNode.name = name
            lineNode.Component(cc.MultiFrame).setFrame(gameHelper.trainTech.getTechLevel(pre) > 0)
            const curWorldPos = ut.convertToNodeAR(curNode, this.treeSv_.content)
            const preWorldPos = ut.convertToNodeAR(preNode, this.treeSv_.content)
            lineNode.angle = ut.getAngle(preWorldPos, curWorldPos)
            const dis = ut.calculationDis(curWorldPos, preWorldPos)
            lineNode.width = dis
            lineNode.setSiblingIndex(0)

        }
    }

    onClickHandler(btn: cc.Button) {
        if (cc.isValid(this._selectedBtn)) {
            this._selectedBtn.Child("selected").active = false
        }
        btn.node.Child("selected").active = true
        this._selectedBtn = btn.node
        viewHelper.showPnl("train/TrainTechOperationPnl", btn.node.Data)
    }
}
